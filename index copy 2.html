<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTable Custom Cell Edit</title>
    <script src="https://unpkg.com/@visactor/vtable/dist/vtable.min.js"></script>
    <style>
        #tableContainer {
            width: 600px;
            height: 400px;
        }
        .custom-edit-popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        .custom-edit-popup input[type="radio"] {
            margin: 10px;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
    </style>
</head>
<body>
    <div id="tableContainer"></div>
    <div class="overlay" id="overlay"></div>
    <div class="custom-edit-popup" id="customEditPopup">
        <h3>Select a value:</h3>
        <div>
            <input type="radio" id="option1" name="editOption" value="Option 1">
            <label for="option1">Option 1</label>
        </div>
        <div>
            <input type="radio" id="option2" name="editOption" value="Option 2">
            <label for="option2">Option 2</label>
        </div>
        <div>
            <input type="radio" id="option3" name="editOption" value="Option 3">
            <label for="option3">Option 3</label>
        </div>
        <button id="confirmButton">Confirm</button>
    </div>

    <script>
        // 准备DOM容器
        const container = document.getElementById('tableContainer');
        
        // 初始化表格
        const tableInstance = new VTable.ListTable({
            container,
            rowSize: 40, // 行高
            columns: [
                { id: 'name', name: 'Name', width: 200 },
                { id: 'value', name: 'Value', width: 200, editable: true }
            ],
            data: [
                { name: 'Item 1', value: 'Option 1' },
                { name: 'Item 2', value: 'Option 2' },
                { name: 'Item 3', value: 'Option 3' }
            ]
        });

        // 获取自定义编辑弹窗元素
        const customEditPopup = document.getElementById('customEditPopup');
        const overlay = document.getElementById('overlay');
        const confirmButton = document.getElementById('confirmButton');
        
        // 存储当前正在编辑的单元格信息
        let currentEditingCell = null;

        // 添加表格单元格双击事件监听
        tableInstance.on('cellDblClick', (params) => {
            const { columnIndex, rowIndex, dataField } = params;
            console.log(111)
            // 确保我们正在编辑的是可编辑的列
            if (dataField === 'value') {
                // 保存当前单元格信息
                currentEditingCell = {
                    columnIndex,
                    rowIndex,
                    dataField
                };

                // 显示弹窗和遮罩
                overlay.style.display = 'block';
                customEditPopup.style.display = 'block';

                // 设置当前值为默认选中
                const currentValue = tableInstance.getCellData(currentEditingCell.rowIndex, currentEditingCell.columnIndex, currentEditingCell.dataField);
                document.querySelectorAll('input[name="editOption"]').forEach(radio => {
                    radio.checked = radio.value === currentValue;
                });
            }
        });

        // 确认按钮点击事件
        confirmButton.addEventListener('click', () => {
            // 获取选中的单选按钮值
            const selectedValue = document.querySelector('input[name="editOption"]:checked').value;
            
            if (currentEditingCell) {
                // 更新表格数据
                tableInstance.updateCellData(
                    currentEditingCell.rowIndex,
                    currentEditingCell.columnIndex,
                    selectedValue
                );
                
                // 关闭弹窗和遮罩
                overlay.style.display = 'none';
                customEditPopup.style.display = 'none';
                
                // 清除当前编辑单元格信息
                currentEditingCell = null;
            }
        });

        // 点击遮罩关闭弹窗
        overlay.addEventListener('click', () => {
            overlay.style.display = 'none';
            customEditPopup.style.display = 'none';
            currentEditingCell = null;
        });
    </script>
</body>
</html>