<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTable Custom Cell Editor</title>
    <script src="https://unpkg.com/@visactor/vtable/dist/vtable.min.js"></script>
    <style>
        #tableContainer {
            position: absolute;
            width: 600px;
            height: 400px;
        }
    </style>
    <script>
        // 定义一个自定义编辑器组件
        class RadioButtonEditor {
            constructor(container, cellData, callback) {
                this.container = container;
                this.cellData = cellData;
                this.callback = callback;
                this.init();
            }

            init() {
                // 清空容器内容
                this.container.innerHTML = '';
                // 创建单选框
                const options = ['Option 1', 'Option 2', 'Option 3'];
                options.forEach(option => {
                    const radio = document.createElement('input');
                    radio.type = 'radio';
                    radio.name = 'radioGroup';
                    radio.value = option;
                    if (option === this.cellData.value) {
                        radio.checked = true;
                    }
                    this.container.appendChild(radio);
                    this.container.appendChild(document.createTextNode(option));
                    this.container.appendChild(document.createElement('br'));
                });

                // 添加事件监听器，当单选框改变时触发回调
                this.container.querySelectorAll('input[type="radio"]').forEach(radio => {
                    radio.addEventListener('change', () => {
                        this.callback(radio.value);
                    });
                });
            }

            destroy() {
                // 清理资源
                this.container.innerHTML = '';
            }
        }

        // 注册自定义编辑器
        VTable.register.editor('radioButtonEditor', RadioButtonEditor);
        // 配置表数据
        const data = [
            { id: 1, name: 'Item 1', status: 'Option 1' },
            { id: 2, name: 'Item 2', status: 'Option 2' },
            { id: 3, name: 'Item 3', status: 'Option 3' }
        ];

        // 配置表结构
        const columns = [
            { field: 'id', title: 'ID' },
            { field: 'name', title: 'Name' },
            {
                field: 'status',
                title: 'Status',
                editor: 'radioButtonEditor', // 指定使用自定义编辑器
                editable: true // 允许编辑
            }
        ];

        const container = document.getElementById('tableContainer');

        // 初始化 VTable
        const tableInstance = new VTable.ListTable({
            el: container,
            data,
            columns,
            cellEdit: {
                trigger: 'dblclick', // 双击触发编辑
                onEdit: function (rowIndex, field, value) {
                    console.log(`Editing row ${rowIndex}, field ${field}, value ${value}`);
                },
                onEditEnd: function (rowIndex, field, value) {
                    console.log(`Edit ended on row ${rowIndex}, field ${field}, value ${value}`);
                }
            }
        });
    </script>
</head>

<body>
    <div id="tableContainer"></div>


</body>

</html>